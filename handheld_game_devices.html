<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Handheld Game Device SVG Collection</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .svg-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .svg-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .svg-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            margin-bottom: 15px;
            background: #f8fafc;
            border-radius: 12px;
        }
        
        .copy-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            border: none;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: linear-gradient(135deg, #4338ca, #6d28d9);
            transform: translateY(-1px);
        }
        
        .copy-btn:active {
            transform: translateY(0);
        }
        
        .copied {
            background: linear-gradient(135deg, #059669, #047857) !important;
        }
        
        .design-title {
            text-align: center;
            margin-bottom: 10px;
            font-weight: 500;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Professional Handheld Game Device SVG Collection</h1>
        <div class="grid">
            
            <!-- Design 1: Classic Retro Style -->
            <div class="svg-card">
                <h3 class="design-title">Classic Retro Style</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#ee5a24;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="10" y="10" width="140" height="100" rx="15" fill="url(#grad1)" stroke="#c44569" stroke-width="2"/>
                        <rect x="25" y="25" width="110" height="60" rx="8" fill="#2c2c54" stroke="#40407a" stroke-width="1"/>
                        <circle cx="35" cy="95" r="8" fill="#ffa726" stroke="#ff8f00" stroke-width="2"/>
                        <circle cx="55" cy="95" r="8" fill="#42a5f5" stroke="#1976d2" stroke-width="2"/>
                        <rect x="100" y="87" width="12" height="4" rx="2" fill="#4caf50"/>
                        <rect x="100" y="93" width="12" height="4" rx="2" fill="#4caf50"/>
                        <rect x="100" y="99" width="12" height="4" rx="2" fill="#4caf50"/>
                        <rect x="120" y="87" width="20" height="16" rx="3" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 0)">Copy SVG Code</button>
            </div>

            <!-- Design 2: Modern Sleek -->
            <div class="svg-card">
                <h3 class="design-title">Modern Sleek</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="15" y="15" width="130" height="90" rx="20" fill="url(#grad2)" stroke="#5a67d8" stroke-width="2"/>
                        <rect x="30" y="30" width="100" height="50" rx="10" fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
                        <circle cx="40" cy="95" r="6" fill="#ed8936" stroke="#dd6b20" stroke-width="1"/>
                        <circle cx="55" cy="95" r="6" fill="#38b2ac" stroke="#319795" stroke-width="1"/>
                        <circle cx="70" cy="95" r="6" fill="#e53e3e" stroke="#c53030" stroke-width="1"/>
                        <rect x="90" y="87" width="25" height="8" rx="4" fill="#48bb78" stroke="#38a169" stroke-width="1"/>
                        <rect x="90" y="97" width="25" height="8" rx="4" fill="#4299e1" stroke="#3182ce" stroke-width="1"/>
                        <circle cx="125" cy="95" r="8" fill="#9f7aea" stroke="#805ad5" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 1)">Copy SVG Code</button>
            </div>

            <!-- Design 3: Gaming Beast -->
            <div class="svg-card">
                <h3 class="design-title">Gaming Beast</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#2d3748;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#1a202c;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="12" y="12" width="136" height="96" rx="18" fill="url(#grad3)" stroke="#4a5568" stroke-width="2"/>
                        <rect x="28" y="28" width="104" height="56" rx="8" fill="#0d1117" stroke="#21262d" stroke-width="1"/>
                        <rect x="20" y="90" width="16" height="16" rx="3" fill="#f56565" stroke="#e53e3e" stroke-width="1"/>
                        <circle cx="50" cy="98" r="7" fill="#48bb78" stroke="#38a169" stroke-width="1"/>
                        <circle cx="68" cy="98" r="7" fill="#4299e1" stroke="#3182ce" stroke-width="1"/>
                        <rect x="85" y="90" width="20" height="6" rx="3" fill="#ed8936"/>
                        <rect x="85" y="98" width="20" height="6" rx="3" fill="#ed8936"/>
                        <circle cx="120" cy="98" r="9" fill="#9f7aea" stroke="#805ad5" stroke-width="2"/>
                        <circle cx="140" cy="98" r="9" fill="#f687b3" stroke="#ed64a6" stroke-width="2"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 2)">Copy SVG Code</button>
            </div>

            <!-- Design 4: Neon Glow -->
            <div class="svg-card">
                <h3 class="design-title">Neon Glow</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad4" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#0f0f23;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
                            </linearGradient>
                            <filter id="glow">
                                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                <feMerge>
                                    <feMergeNode in="coloredBlur"/>
                                    <feMergeNode in="SourceGraphic"/>
                                </feMerge>
                            </filter>
                        </defs>
                        <rect x="14" y="14" width="132" height="92" rx="16" fill="url(#grad4)" stroke="#16213e" stroke-width="2"/>
                        <rect x="30" y="30" width="100" height="52" rx="8" fill="#000" stroke="#00ffff" stroke-width="2" filter="url(#glow)"/>
                        <circle cx="45" cy="95" r="8" fill="#ff0080" filter="url(#glow)"/>
                        <circle cx="65" cy="95" r="8" fill="#00ff80" filter="url(#glow)"/>
                        <rect x="85" y="87" width="18" height="6" rx="3" fill="#ffff00" filter="url(#glow)"/>
                        <rect x="85" y="95" width="18" height="6" rx="3" fill="#ff8000" filter="url(#glow)"/>
                        <circle cx="115" cy="95" r="9" fill="#8000ff" filter="url(#glow)"/>
                        <circle cx="135" cy="95" r="9" fill="#ff0040" filter="url(#glow)"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 3)">Copy SVG Code</button>
            </div>

            <!-- Design 5: Gradient Pro -->
            <div class="svg-card">
                <h3 class="design-title">Gradient Pro</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad5" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#fecfef;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="16" y="16" width="128" height="88" rx="22" fill="url(#grad5)" stroke="#f093fb" stroke-width="2"/>
                        <rect x="32" y="32" width="96" height="48" rx="12" fill="#4a00e0" stroke="#8e2de2" stroke-width="1"/>
                        <rect x="25" y="88" width="14" height="14" rx="4" fill="#ff6b6b" stroke="#ee5a24" stroke-width="1"/>
                        <circle cx="55" cy="95" r="7" fill="#4ecdc4" stroke="#26d0ce" stroke-width="1"/>
                        <circle cx="75" cy="95" r="7" fill="#45b7d1" stroke="#3498db" stroke-width="1"/>
                        <rect x="90" y="88" width="22" height="5" rx="2.5" fill="#f39c12"/>
                        <rect x="90" y="95" width="22" height="5" rx="2.5" fill="#e67e22"/>
                        <circle cx="125" cy="95" r="8" fill="#9b59b6" stroke="#8e44ad" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 4)">Copy SVG Code</button>
            </div>

            <!-- Design 6: Metallic Chrome -->
            <div class="svg-card">
                <h3 class="design-title">Metallic Chrome</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad6" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#bdc3c7;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#ecf0f1;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#95a5a6;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="18" y="18" width="124" height="84" rx="20" fill="url(#grad6)" stroke="#7f8c8d" stroke-width="2"/>
                        <rect x="34" y="34" width="92" height="44" rx="10" fill="#2c3e50" stroke="#34495e" stroke-width="1"/>
                        <circle cx="45" cy="92" r="8" fill="#e74c3c" stroke="#c0392b" stroke-width="1"/>
                        <circle cx="65" cy="92" r="8" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
                        <rect x="85" y="84" width="20" height="6" rx="3" fill="#f39c12"/>
                        <rect x="85" y="92" width="20" height="6" rx="3" fill="#e67e22"/>
                        <circle cx="115" cy="92" r="9" fill="#9b59b6" stroke="#8e44ad" stroke-width="1"/>
                        <circle cx="135" cy="92" r="9" fill="#1abc9c" stroke="#16a085" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 5)">Copy SVG Code</button>
            </div>

            <!-- Design 7: Ocean Blue -->
            <div class="svg-card">
                <h3 class="design-title">Ocean Blue</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad7" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#0984e3;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="20" y="20" width="120" height="80" rx="16" fill="url(#grad7)" stroke="#2d3436" stroke-width="2"/>
                        <rect x="35" y="35" width="90" height="40" rx="8" fill="#2d3436" stroke="#636e72" stroke-width="1"/>
                        <circle cx="50" cy="90" r="7" fill="#fd79a8" stroke="#e84393" stroke-width="1"/>
                        <circle cx="70" cy="90" r="7" fill="#fdcb6e" stroke="#e17055" stroke-width="1"/>
                        <rect x="90" y="83" width="18" height="5" rx="2.5" fill="#00b894"/>
                        <rect x="90" y="90" width="18" height="5" rx="2.5" fill="#00cec9"/>
                        <rect x="90" y="97" width="18" height="5" rx="2.5" fill="#55a3ff"/>
                        <circle cx="120" cy="90" r="8" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 6)">Copy SVG Code</button>
            </div>

            <!-- Design 8: Sunset Orange -->
            <div class="svg-card">
                <h3 class="design-title">Sunset Orange</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad8" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fab1a0;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#ff7675;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e17055;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="22" y="22" width="116" height="76" rx="18" fill="url(#grad8)" stroke="#d63031" stroke-width="2"/>
                        <rect x="37" y="37" width="86" height="36" rx="6" fill="#2d3436" stroke="#636e72" stroke-width="1"/>
                        <rect x="30" y="82" width="12" height="12" rx="3" fill="#00b894" stroke="#00a085" stroke-width="1"/>
                        <circle cx="55" cy="88" r="6" fill="#0984e3" stroke="#74b9ff" stroke-width="1"/>
                        <circle cx="75" cy="88" r="6" fill="#6c5ce7" stroke="#a29bfe" stroke-width="1"/>
                        <rect x="90" y="82" width="16" height="4" rx="2" fill="#fdcb6e"/>
                        <rect x="90" y="88" width="16" height="4" rx="2" fill="#e17055"/>
                        <rect x="90" y="94" width="16" height="4" rx="2" fill="#fd79a8"/>
                        <circle cx="120" cy="88" r="7" fill="#00cec9" stroke="#55efc4" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 7)">Copy SVG Code</button>
            </div>

            <!-- Design 9: Forest Green -->
            <div class="svg-card">
                <h3 class="design-title">Forest Green</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad9" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#00b894;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#00a085;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="24" y="24" width="112" height="72" rx="14" fill="url(#grad9)" stroke="#2d3436" stroke-width="2"/>
                        <rect x="39" y="39" width="82" height="32" rx="6" fill="#2d3436" stroke="#636e72" stroke-width="1"/>
                        <circle cx="50" cy="85" r="7" fill="#e17055" stroke="#d63031" stroke-width="1"/>
                        <circle cx="70" cy="85" r="7" fill="#fdcb6e" stroke="#e84393" stroke-width="1"/>
                        <rect x="85" y="78" width="14" height="4" rx="2" fill="#74b9ff"/>
                        <rect x="85" y="84" width="14" height="4" rx="2" fill="#0984e3"/>
                        <rect x="85" y="90" width="14" height="4" rx="2" fill="#6c5ce7"/>
                        <circle cx="110" cy="85" r="6" fill="#fd79a8" stroke="#e84393" stroke-width="1"/>
                        <circle cx="125" cy="85" r="6" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 8)">Copy SVG Code</button>
            </div>

            <!-- Design 10: Purple Haze -->
            <div class="svg-card">
                <h3 class="design-title">Purple Haze</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad10" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#a29bfe;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#6c5ce7;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="26" y="26" width="108" height="68" rx="12" fill="url(#grad10)" stroke="#2d3436" stroke-width="2"/>
                        <rect x="41" y="41" width="78" height="28" rx="4" fill="#2d3436" stroke="#636e72" stroke-width="1"/>
                        <rect x="35" y="78" width="10" height="10" rx="2" fill="#fd79a8" stroke="#e84393" stroke-width="1"/>
                        <circle cx="60" cy="83" r="5" fill="#00cec9" stroke="#55efc4" stroke-width="1"/>
                        <circle cx="80" cy="83" r="5" fill="#fdcb6e" stroke="#e17055" stroke-width="1"/>
                        <rect x="95" y="78" width="12" height="3" rx="1.5" fill="#74b9ff"/>
                        <rect x="95" y="83" width="12" height="3" rx="1.5" fill="#0984e3"/>
                        <rect x="95" y="88" width="12" height="3" rx="1.5" fill="#00b894"/>
                        <circle cx="120" cy="83" r="6" fill="#e17055" stroke="#d63031" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 9)">Copy SVG Code</button>
            </div>

            <!-- Design 11: Cyber Pink -->
            <div class="svg-card">
                <h3 class="design-title">Cyber Pink</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad11" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fd79a8;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e84393;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="28" y="28" width="104" height="64" rx="10" fill="url(#grad11)" stroke="#2d3436" stroke-width="2"/>
                        <rect x="43" y="43" width="74" height="24" rx="2" fill="#2d3436" stroke="#636e72" stroke-width="1"/>
                        <circle cx="55" cy="80" r="6" fill="#00cec9" stroke="#55efc4" stroke-width="1"/>
                        <circle cx="75" cy="80" r="6" fill="#fdcb6e" stroke="#e17055" stroke-width="1"/>
                        <rect x="90" y="74" width="10" height="3" rx="1.5" fill="#74b9ff"/>
                        <rect x="90" y="79" width="10" height="3" rx="1.5" fill="#0984e3"/>
                        <rect x="90" y="84" width="10" height="3" rx="1.5" fill="#6c5ce7"/>
                        <circle cx="110" cy="80" r="5" fill="#00b894" stroke="#00a085" stroke-width="1"/>
                        <circle cx="125" cy="80" r="5" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 10)">Copy SVG Code</button>
            </div>

            <!-- Design 12: Golden Hour -->
            <div class="svg-card">
                <h3 class="design-title">Golden Hour</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad12" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fdcb6e;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e17055;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="30" y="30" width="100" height="60" rx="8" fill="url(#grad12)" stroke="#2d3436" stroke-width="2"/>
                        <rect x="45" y="45" width="70" height="20" rx="2" fill="#2d3436" stroke="#636e72" stroke-width="1"/>
                        <rect x="40" y="75" width="8" height="8" rx="2" fill="#fd79a8" stroke="#e84393" stroke-width="1"/>
                        <circle cx="65" cy="79" r="4" fill="#74b9ff" stroke="#0984e3" stroke-width="1"/>
                        <circle cx="85" cy="79" r="4" fill="#00cec9" stroke="#55efc4" stroke-width="1"/>
                        <rect x="100" y="75" width="8" height="2" rx="1" fill="#6c5ce7"/>
                        <rect x="100" y="79" width="8" height="2" rx="1" fill="#a29bfe"/>
                        <rect x="100" y="83" width="8" height="2" rx="1" fill="#00b894"/>
                        <circle cx="120" cy="79" r="4" fill="#e17055" stroke="#d63031" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 11)">Copy SVG Code</button>
            </div>

            <!-- Design 13: Arctic White -->
            <div class="svg-card">
                <h3 class="design-title">Arctic White</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad13" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ddd6fe;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c7d2fe;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="32" y="32" width="96" height="56" rx="6" fill="url(#grad13)" stroke="#6366f1" stroke-width="2"/>
                        <rect x="47" y="47" width="66" height="16" rx="1" fill="#1e293b" stroke="#475569" stroke-width="1"/>
                        <circle cx="60" cy="77" r="5" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                        <circle cx="80" cy="77" r="5" fill="#3b82f6" stroke="#2563eb" stroke-width="1"/>
                        <rect x="95" y="72" width="6" height="2" rx="1" fill="#10b981"/>
                        <rect x="95" y="76" width="6" height="2" rx="1" fill="#06b6d4"/>
                        <rect x="95" y="80" width="6" height="2" rx="1" fill="#8b5cf6"/>
                        <circle cx="115" cy="77" r="4" fill="#f59e0b" stroke="#d97706" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 12)">Copy SVG Code</button>
            </div>

            <!-- Design 14: Midnight Black -->
            <div class="svg-card">
                <h3 class="design-title">Midnight Black</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad14" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#374151;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#111827;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="34" y="34" width="92" height="52" rx="4" fill="url(#grad14)" stroke="#4b5563" stroke-width="2"/>
                        <rect x="49" y="49" width="62" height="12" rx="1" fill="#000" stroke="#1f2937" stroke-width="1"/>
                        <rect x="45" y="70" width="6" height="6" rx="1" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                        <circle cx="70" cy="73" r="3" fill="#22c55e" stroke="#16a34a" stroke-width="1"/>
                        <circle cx="90" cy="73" r="3" fill="#3b82f6" stroke="#2563eb" stroke-width="1"/>
                        <rect x="105" y="70" width="4" height="1" rx="0.5" fill="#f59e0b"/>
                        <rect x="105" y="73" width="4" height="1" rx="0.5" fill="#ef4444"/>
                        <rect x="105" y="76" width="4" height="1" rx="0.5" fill="#8b5cf6"/>
                        <circle cx="120" cy="73" r="3" fill="#06b6d4" stroke="#0891b2" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 13)">Copy SVG Code</button>
            </div>

            <!-- Design 15: Rainbow Spectrum -->
            <div class="svg-card">
                <h3 class="design-title">Rainbow Spectrum</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad15" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ff0000;stop-opacity:1" />
                                <stop offset="16%" style="stop-color:#ff8000;stop-opacity:1" />
                                <stop offset="33%" style="stop-color:#ffff00;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#00ff00;stop-opacity:1" />
                                <stop offset="66%" style="stop-color:#0080ff;stop-opacity:1" />
                                <stop offset="83%" style="stop-color:#8000ff;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#ff00ff;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="36" y="36" width="88" height="48" rx="2" fill="url(#grad15)" stroke="#000" stroke-width="2"/>
                        <rect x="51" y="51" width="58" height="8" rx="1" fill="#000" stroke="#333" stroke-width="1"/>
                        <circle cx="65" cy="71" r="4" fill="#fff" stroke="#000" stroke-width="1"/>
                        <circle cx="85" cy="71" r="4" fill="#000" stroke="#fff" stroke-width="1"/>
                        <rect x="100" y="67" width="3" height="1" rx="0.5" fill="#fff"/>
                        <rect x="100" y="70" width="3" height="1" rx="0.5" fill="#000"/>
                        <rect x="100" y="73" width="3" height="1" rx="0.5" fill="#fff"/>
                        <circle cx="115" cy="71" r="3" fill="#808080" stroke="#000" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 14)">Copy SVG Code</button>
            </div>

            <!-- Design 16: Holographic -->
            <div class="svg-card">
                <h3 class="design-title">Holographic</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad16" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#c084fc;stop-opacity:1" />
                                <stop offset="25%" style="stop-color:#60a5fa;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#34d399;stop-opacity:1" />
                                <stop offset="75%" style="stop-color:#fbbf24;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#f87171;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="38" y="38" width="84" height="44" rx="1" fill="url(#grad16)" stroke="#1f2937" stroke-width="2"/>
                        <rect x="53" y="53" width="54" height="4" rx="1" fill="#000" stroke="#374151" stroke-width="1"/>
                        <rect x="50" y="68" width="4" height="4" rx="1" fill="#fbbf24" stroke="#f59e0b" stroke-width="1"/>
                        <circle cx="70" cy="70" r="2" fill="#34d399" stroke="#10b981" stroke-width="1"/>
                        <circle cx="90" cy="70" r="2" fill="#60a5fa" stroke="#3b82f6" stroke-width="1"/>
                        <rect x="105" y="68" width="2" height="0.5" rx="0.25" fill="#c084fc"/>
                        <rect x="105" y="70" width="2" height="0.5" rx="0.25" fill="#f87171"/>
                        <rect x="105" y="72" width="2" height="0.5" rx="0.25" fill="#34d399"/>
                        <circle cx="115" cy="70" r="2" fill="#f87171" stroke="#ef4444" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 15)">Copy SVG Code</button>
            </div>

            <!-- Design 17: Retro Wave -->
            <div class="svg-card">
                <h3 class="design-title">Retro Wave</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad17" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ff006e;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#8338ec;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#3a86ff;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="40" y="40" width="80" height="40" rx="1" fill="url(#grad17)" stroke="#000" stroke-width="2"/>
                        <rect x="55" y="55" width="50" height="2" rx="1" fill="#000" stroke="#333" stroke-width="1"/>
                        <circle cx="70" cy="68" r="3" fill="#ffbe0b" stroke="#fb8500" stroke-width="1"/>
                        <circle cx="90" cy="68" r="3" fill="#06ffa5" stroke="#40916c" stroke-width="1"/>
                        <rect x="105" y="65" width="1" height="0.5" rx="0.25" fill="#ffbe0b"/>
                        <rect x="105" y="67" width="1" height="0.5" rx="0.25" fill="#ff006e"/>
                        <rect x="105" y="69" width="1" height="0.5" rx="0.25" fill="#3a86ff"/>
                        <circle cx="115" cy="68" r="2" fill="#ff9500" stroke="#ff6d00" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 16)">Copy SVG Code</button>
            </div>

            <!-- Design 18: Crystal Clear -->
            <div class="svg-card">
                <h3 class="design-title">Crystal Clear</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad18" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#f0f9ff;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#e0f2fe;stop-opacity:0.9" />
                            </linearGradient>
                        </defs>
                        <rect x="42" y="42" width="76" height="36" rx="1" fill="url(#grad18)" stroke="#0ea5e9" stroke-width="2"/>
                        <rect x="57" y="57" width="46" height="1" rx="0.5" fill="#0369a1" stroke="#0284c7" stroke-width="1"/>
                        <rect x="55" y="66" width="3" height="3" rx="0.5" fill="#dc2626" stroke="#b91c1c" stroke-width="1"/>
                        <circle cx="75" cy="67" r="2" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                        <circle cx="95" cy="67" r="2" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                        <rect x="105" y="65" width="1" height="0.3" rx="0.15" fill="#f59e0b"/>
                        <rect x="105" y="67" width="1" height="0.3" rx="0.15" fill="#dc2626"/>
                        <rect x="105" y="69" width="1" height="0.3" rx="0.15" fill="#7c3aed"/>
                        <circle cx="115" cy="67" r="1.5" fill="#059669" stroke="#047857" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 17)">Copy SVG Code</button>
            </div>

            <!-- Design 19: Volcanic Red -->
            <div class="svg-card">
                <h3 class="design-title">Volcanic Red</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad19" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#991b1b;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#7f1d1d;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="44" y="44" width="72" height="32" rx="1" fill="url(#grad19)" stroke="#450a0a" stroke-width="2"/>
                        <rect x="59" y="59" width="42" height="1" rx="0.5" fill="#fef2f2" stroke="#fecaca" stroke-width="1"/>
                        <circle cx="75" cy="65" r="2" fill="#fbbf24" stroke="#f59e0b" stroke-width="1"/>
                        <circle cx="95" cy="65" r="2" fill="#06b6d4" stroke="#0891b2" stroke-width="1"/>
                        <rect x="105" y="63" width="1" height="0.4" rx="0.2" fill="#22c55e"/>
                        <rect x="105" y="65" width="1" height="0.4" rx="0.2" fill="#3b82f6"/>
                        <rect x="105" y="67" width="1" height="0.4" rx="0.2" fill="#a855f7"/>
                        <circle cx="115" cy="65" r="1.5" fill="#f97316" stroke="#ea580c" stroke-width="1"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 18)">Copy SVG Code</button>
            </div>

            <!-- Design 20: Electric Blue -->
            <div class="svg-card">
                <h3 class="design-title">Electric Blue</h3>
                <div class="svg-container">
                    <svg width="160" height="120" viewBox="0 0 160 120" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad20" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#1e3a8a;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#1e293b;stop-opacity:1" />
                            </linearGradient>
                            <filter id="electric">
                                <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                                <feMerge>
                                    <feMergeNode in="coloredBlur"/>
                                    <feMergeNode in="SourceGraphic"/>
                                </feMerge>
                            </filter>
                        </defs>
                        <rect x="46" y="46" width="68" height="28" rx="1" fill="url(#grad20)" stroke="#0f172a" stroke-width="2"/>
                        <rect x="61" y="61" width="38" height="1" rx="0.5" fill="#60a5fa" stroke="#3b82f6" stroke-width="1" filter="url(#electric)"/>
                        <circle cx="77" cy="63" r="1.5" fill="#fbbf24" stroke="#f59e0b" stroke-width="1" filter="url(#electric)"/>
                        <circle cx="97" cy="63" r="1.5" fill="#10b981" stroke="#059669" stroke-width="1" filter="url(#electric)"/>
                        <rect x="105" y="61" width="0.8" height="0.3" rx="0.15" fill="#f472b6" filter="url(#electric)"/>
                        <rect x="105" y="63" width="0.8" height="0.3" rx="0.15" fill="#a78bfa" filter="url(#electric)"/>
                        <rect x="105" y="65" width="0.8" height="0.3" rx="0.15" fill="#34d399" filter="url(#electric)"/>
                        <circle cx="115" cy="63" r="1" fill="#ef4444" stroke="#dc2626" stroke-width="1" filter="url(#electric)"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 19)">Copy SVG Code</button>
            </div>

        </div>
    </div>

    <script>
        function copySVG(button, index) {
            // Find the SVG element in the same card as the button
            const card = button.closest('.svg-card');
            const svgElement = card.querySelector('svg');

            if (svgElement) {
                // Get the outer HTML of the SVG element
                const svgCode = svgElement.outerHTML;

                // Copy to clipboard
                navigator.clipboard.writeText(svgCode).then(function() {
                    // Change button text and style temporarily
                    const originalText = button.textContent;
                    button.textContent = 'Copied!';
                    button.classList.add('copied');

                    // Reset after 2 seconds
                    setTimeout(function() {
                        button.textContent = originalText;
                        button.classList.remove('copied');
                    }, 2000);
                }).catch(function(err) {
                    console.error('Failed to copy: ', err);
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = svgCode;
                    document.body.appendChild(textArea);
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        const originalText = button.textContent;
                        button.textContent = 'Copied!';
                        button.classList.add('copied');
                        setTimeout(function() {
                            button.textContent = originalText;
                            button.classList.remove('copied');
                        }, 2000);
                    } catch (err) {
                        alert('Failed to copy SVG code. Please try again.');
                    }
                    document.body.removeChild(textArea);
                });
            } else {
                alert('SVG not found. Please try again.');
            }
        }

        function copySVG(button, index) {
            // Get the SVG code
            const svgCode = svgCodes[index];

            // Copy to clipboard
            navigator.clipboard.writeText(svgCode).then(function() {
                // Change button text and style temporarily
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.add('copied');

                // Reset after 2 seconds
                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            }).catch(function(err) {
                console.error('Failed to copy: ', err);
                alert('Failed to copy SVG code. Please try again.');
            });
        }
    </script>
</body>
</html>

